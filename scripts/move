#!/usr/bin/env ruby

require "byebug"
require "fileutils"
require "pathname"
require "front_matter_parser"
require "yaml"
require "date"

root = Pathname(__dir__).join("../content").realpath
files = Dir.glob("#{root}/posts/**/*.md").map(&Pathname.method(:new))

files.each do |file_path|
  begin
    source_file = FrontMatterParser::Parser.parse_file(file_path.to_s)
    source_date = (date = source_file.front_matter["date"]).is_a?(String) ? DateTime.parse(date) : date
    source_title = file_path.dirname.basename
    source_slug = "/#{source_date.strftime("%Y/%m/%d")}/#{source_title}"

    new_front_matter = source_file.front_matter.merge(
      "slug" => "#{source_title}",
      "tags" => ["archives"] + source_file.front_matter["tags"],
      "aliases" => [source_slug]
    )

    front_matter_hn = new_front_matter.merge(
      "domain" => "solnic.dev",
      "tags" => new_front_matter["tags"].join(", "),
      "hideFromHashnodeCommunity" => true,
      "seriesSlug" => "archives"
    ).slice("title", "date", "tags", "slug", "seriesSlug", "domain", "hideFromHashnodeCommunity")

    new_path = root.join("#{source_title}/index.md")
    new_path_hn = root.join("hashnode/archives")
    new_content = "#{new_front_matter.to_yaml}---\n\n#{source_file.content}"
    new_content_hashnode = "#{front_matter_hn.to_yaml}---\n\n#{source_file.content}"

    puts "#{source_slug} => #{new_path}"

    FileUtils.rm(file_path)
    FileUtils.mkdir_p(new_path.dirname)
    FileUtils.mkdir_p(new_path_hn)

    File.open(new_path, 'w') { |f| f.write(new_content) }
    File.open(new_path_hn.join("#{source_title}.md"), 'w') { |f| f.write(new_content_hashnode) }
  rescue => e
    puts e.inspect
    puts source_file.front_matter
    raise e
  end
end
