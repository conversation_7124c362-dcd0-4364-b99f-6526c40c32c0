#!/usr/bin/env ruby

require 'nokogiri'
require 'open-uri'
require 'fileutils'

def usage
  puts "Usage: #{$0} <guid-part>"
  puts "Example: #{$0} capturing"
  exit 1
end

usage if ARGV.empty?

guid_match = ARGV[0]
rss_url = "https://solnic.dev/posts/index.xml"
tmp_dir = "tmp"

begin
  # Create tmp directory if it doesn't exist
  FileUtils.mkdir_p(tmp_dir)

  # Fetch and parse RSS feed
  doc = Nokogiri::XML(URI.open(rss_url))

  # Find the item with matching guid
  item = doc.xpath("//item").find do |item|
    guid = item.at_xpath("guid").text
    guid.include?(guid_match)
  end

  if item
    content = item.at_xpath("content:encoded").text
    title = item.at_xpath("title").text
    guid = item.at_xpath("guid").text.split("/").last
    output_file = File.join(tmp_dir, "#{guid}.html")

    html = <<~HTML
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>#{title}</title>
        <style>
          body {
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.5;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
          }
          /* Override color only for spans in code blocks that don't have color set */
          .highlight pre code span:not([style*="color"]) {
            color: #c8d3f5 !important;
          }
          /* Ensure code blocks have rounded corners */
          .highlight pre {
            border-radius: 6px !important;
            overflow: hidden !important;
          }
        </style>
      </head>
      <body>
        #{content}
      </body>
      </html>
    HTML

    File.write(output_file, html)
    puts "Content extracted to #{output_file}"
  else
    puts "No post found with guid matching '#{guid_match}'"
    exit 1
  end
rescue OpenURI::HTTPError => e
  puts "Failed to fetch RSS feed: #{e.message}"
  exit 1
rescue => e
  puts "Error: #{e.message}"
  exit 1
end
